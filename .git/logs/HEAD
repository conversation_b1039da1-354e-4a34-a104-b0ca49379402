0000000000000000000000000000000000000000 7d99fd00778cd74e5c957bb8fd9d1c4baa6e585c volehong-sc <<EMAIL>> 1753429061 +0700	clone: from github.com:decurret-lab/dcbg-dcjpy-contract.git
7d99fd00778cd74e5c957bb8fd9d1c4baa6e585c 2ee0d06267ef2074b2c7782463cd0ec24aa169f6 volehong-sc <<EMAIL>> 1756205134 +0700	checkout: moving from develop to feature/DCPF-48540/token-external-storage-pattern
2ee0d06267ef2074b2c7782463cd0ec24aa169f6 2ee0d06267ef2074b2c7782463cd0ec24aa169f6 volehong-sc <<EMAIL>> 1756205182 +0700	checkout: moving from feature/DCPF-48540/token-external-storage-pattern to feature/DCPF-48540/token-external-storage-pattern-fix1
2ee0d06267ef2074b2c7782463cd0ec24aa169f6 2ee0d06267ef2074b2c7782463cd0ec24aa169f6 volehong-sc <<EMAIL>> 1756205185 +0700	checkout: moving from feature/DCPF-48540/token-external-storage-pattern-fix1 to feature/DCPF-48540/token-external-storage-pattern
2ee0d06267ef2074b2c7782463cd0ec24aa169f6 cd595c6d173d18ea7f6311724e1a338a2f332163 volehong-sc <<EMAIL>> 1756205194 +0700	rebase (start): checkout refs/remotes/origin/feature/DCPF-52682/issuer-external-storage-pattern-deploy
cd595c6d173d18ea7f6311724e1a338a2f332163 530e612affdc1c993792d55f6dcea4799ab607d4 volehong-sc <<EMAIL>> 1756205256 +0700	rebase (continue): DCPF-48538: testファイル内のコントラクト初期化処理をIssuer分割に対応
530e612affdc1c993792d55f6dcea4799ab607d4 7cea8e2d3808f77acb4ff8bea99f13889ebb2221 volehong-sc <<EMAIL>> 1756205271 +0700	rebase (pick): DCPF-48540: TokenコントラクトをLogicコントラクトとStorageコントラクトに分割
7cea8e2d3808f77acb4ff8bea99f13889ebb2221 872ca84ebd9bde93c87ada27e61045182654e5bd volehong-sc <<EMAIL>> 1756205271 +0700	rebase (pick): DCPF-48540: testファイル内のコントラクト初期化処理をValidator分割に対応
872ca84ebd9bde93c87ada27e61045182654e5bd 872ca84ebd9bde93c87ada27e61045182654e5bd volehong-sc <<EMAIL>> 1756205271 +0700	rebase (finish): returning to refs/heads/feature/DCPF-48540/token-external-storage-pattern
872ca84ebd9bde93c87ada27e61045182654e5bd 2ee0d06267ef2074b2c7782463cd0ec24aa169f6 volehong-sc <<EMAIL>> 1756207237 +0700	checkout: moving from feature/DCPF-48540/token-external-storage-pattern to feature/DCPF-48540/token-external-storage-pattern-fix1
2ee0d06267ef2074b2c7782463cd0ec24aa169f6 cd595c6d173d18ea7f6311724e1a338a2f332163 volehong-sc <<EMAIL>> 1756207278 +0700	checkout: moving from feature/DCPF-48540/token-external-storage-pattern-fix1 to feature/DCPF-48540/token-external-storage-pattern
cd595c6d173d18ea7f6311724e1a338a2f332163 b90996e14fe03a5c8b3c0d362f985f81247d361a volehong-sc <<EMAIL>> 1756207304 +0700	cherry-pick: DCPF-48540: TokenコントラクトをLogicコントラクトとStorageコントラクトに分割
b90996e14fe03a5c8b3c0d362f985f81247d361a dd9dac736095cb1c46fd77041632379c6d46fce4 volehong-sc <<EMAIL>> 1756207310 +0700	cherry-pick: DCPF-48540: testファイル内のコントラクト初期化処理をValidator分割に対応
dd9dac736095cb1c46fd77041632379c6d46fce4 97716a38360c46fe19b5344625e32de13f777b9e volehong-sc <<EMAIL>> 1756207652 +0700	commit (amend): DCPF-48540: testファイル内のコントラクト初期化処理をToken分割に対応
97716a38360c46fe19b5344625e32de13f777b9e cd595c6d173d18ea7f6311724e1a338a2f332163 volehong-sc <<EMAIL>> 1756207849 +0700	checkout: moving from feature/DCPF-48540/token-external-storage-pattern to feature/DCPF-52682/issuer-external-storage-pattern-deploy
cd595c6d173d18ea7f6311724e1a338a2f332163 b40fb9f5c4174575fa9df2eb5abbede2db30354c volehong-sc <<EMAIL>> 1756207886 +0700	branch: Reset to origin/feature/DCPF-52682/issuer-external-storage-pattern-deploy
b40fb9f5c4174575fa9df2eb5abbede2db30354c b40fb9f5c4174575fa9df2eb5abbede2db30354c volehong-sc <<EMAIL>> 1756207886 +0700	checkout: moving from feature/DCPF-52682/issuer-external-storage-pattern-deploy to feature/DCPF-52682/issuer-external-storage-pattern-deploy
b40fb9f5c4174575fa9df2eb5abbede2db30354c b40fb9f5c4174575fa9df2eb5abbede2db30354c volehong-sc <<EMAIL>> 1756207903 +0700	rebase (start): checkout HEAD
b40fb9f5c4174575fa9df2eb5abbede2db30354c 97716a38360c46fe19b5344625e32de13f777b9e volehong-sc <<EMAIL>> 1756207964 +0700	rebase (abort): returning to refs/heads/feature/DCPF-48540/token-external-storage-pattern
97716a38360c46fe19b5344625e32de13f777b9e 97716a38360c46fe19b5344625e32de13f777b9e volehong-sc <<EMAIL>> 1756207984 +0700	checkout: moving from feature/DCPF-48540/token-external-storage-pattern to feature/DCPF-48540/token-external-storage-pattern-fix2
97716a38360c46fe19b5344625e32de13f777b9e 97716a38360c46fe19b5344625e32de13f777b9e volehong-sc <<EMAIL>> 1756207987 +0700	checkout: moving from feature/DCPF-48540/token-external-storage-pattern-fix2 to feature/DCPF-48540/token-external-storage-pattern
97716a38360c46fe19b5344625e32de13f777b9e b40fb9f5c4174575fa9df2eb5abbede2db30354c volehong-sc <<EMAIL>> 1756208020 +0700	checkout: moving from feature/DCPF-48540/token-external-storage-pattern to feature/DCPF-52682/issuer-external-storage-pattern-deploy
b40fb9f5c4174575fa9df2eb5abbede2db30354c b40fb9f5c4174575fa9df2eb5abbede2db30354c volehong-sc <<EMAIL>> 1756208033 +0700	checkout: moving from feature/DCPF-52682/issuer-external-storage-pattern-deploy to feature/DCPF-48540/token-external-storage-pattern
b40fb9f5c4174575fa9df2eb5abbede2db30354c 9a4c74aaf2a72ac72565d5fd0e11040a5c517ebf volehong-sc <<EMAIL>> 1756208042 +0700	cherry-pick: DCPF-48540: TokenコントラクトをLogicコントラクトとStorageコントラクトに分割
9a4c74aaf2a72ac72565d5fd0e11040a5c517ebf ae545b050094c2924a6bb7ec74ba0584fc3fe010 volehong-sc <<EMAIL>> 1756208047 +0700	cherry-pick: DCPF-48540: testファイル内のコントラクト初期化処理をToken分割に対応
ae545b050094c2924a6bb7ec74ba0584fc3fe010 7d99fd00778cd74e5c957bb8fd9d1c4baa6e585c volehong-sc <<EMAIL>> 1756351043 +0700	checkout: moving from feature/DCPF-48540/token-external-storage-pattern to develop
7d99fd00778cd74e5c957bb8fd9d1c4baa6e585c 9f5f5640a90c1a566d920dc5e7bd1345ab839cb0 volehong-sc <<EMAIL>> 1756351344 +0700	checkout: moving from develop to feature/DCPF-53020/renewableenergytoken-external-storage-pattern
9f5f5640a90c1a566d920dc5e7bd1345ab839cb0 7d99fd00778cd74e5c957bb8fd9d1c4baa6e585c volehong-sc <<EMAIL>> 1756353017 +0700	checkout: moving from feature/DCPF-53020/renewableenergytoken-external-storage-pattern to develop
7d99fd00778cd74e5c957bb8fd9d1c4baa6e585c 9f5f5640a90c1a566d920dc5e7bd1345ab839cb0 volehong-sc <<EMAIL>> 1756353336 +0700	checkout: moving from develop to feature/DCPF-53020/renewableenergytoken-external-storage-pattern
9f5f5640a90c1a566d920dc5e7bd1345ab839cb0 ae545b050094c2924a6bb7ec74ba0584fc3fe010 volehong-sc <<EMAIL>> 1756354379 +0700	checkout: moving from feature/DCPF-53020/renewableenergytoken-external-storage-pattern to feature/DCPF-48540/token-external-storage-pattern
ae545b050094c2924a6bb7ec74ba0584fc3fe010 81a7c105da14c30deb77df81bcda7ae0527d0600 volehong-sc <<EMAIL>> ********** +0700	checkout: moving from feature/DCPF-48540/token-external-storage-pattern to feature/DCPF-53006/token-external-storage-pattern-deploy
81a7c105da14c30deb77df81bcda7ae0527d0600 6a4aec1acc9936008bb603585aa10e12ff8c33ce volehong-sc <<EMAIL>> ********** +0700	commit: convert business zone account
6a4aec1acc9936008bb603585aa10e12ff8c33ce b81fda5fcc5f5f36a0feeac69411d13d6a8c545b volehong-sc <<EMAIL>> ********** +0700	commit: convert business zone account
