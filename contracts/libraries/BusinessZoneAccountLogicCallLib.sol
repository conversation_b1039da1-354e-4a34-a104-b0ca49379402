// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;

import "../interfaces/IContractManager.sol";
import "../interfaces/IBusinessZoneAccountStorage.sol";
import "../interfaces/Struct.sol";
import "../interfaces/Error.sol";
import "../interfaces/Constant.sol";

/**
 * @dev BusinessZoneAccountLogicCallLibライブラリ
 *      BusinessZoneAccountのview/pure関数を実装するヘルパーライブラリ
 *      ブロックチェーンデータの変更は行わない
 */
library BusinessZoneAccountLogicCallLib {
    /** @dev 未登録の場合にて返す空の値 */
    bytes32 private constant EMPTY_VALUE = 0x00;

    ///////////////////////////////////
    // 共通バリデーション関数
    ///////////////////////////////////

    /**
     * @dev 送信者がValidatorコントラクトかどうかを検証
     * @param contractManager ContractManager参照
     * @param sender 送信者アドレス
     */
    function requireValidatorContract(IContractManager contractManager, address sender) internal view {
        require(
            sender == address(contractManager.validator()),
            Error.GA0019_NOT_VALIDATOR_CONTRACT
        );
    }

    /**
     * @dev 送信者がTokenコントラクト（token or ibcToken）かどうかを検証
     * @param contractManager ContractManager参照
     * @param sender 送信者アドレス
     */
    function requireTokenContract(IContractManager contractManager, address sender) internal view {
        require(
            (sender == address(contractManager.token()) ||
                sender == address(contractManager.ibcToken())),
            Error.GA0016_NOT_TOKEN_CONTRACT
        );
    }

    /**
     * @dev 送信者がIBCTokenコントラクトかどうかを検証
     * @param contractManager ContractManager参照
     * @param sender 送信者アドレス
     */
    function requireIBCTokenContract(IContractManager contractManager, address sender) internal view {
        require(sender == address(contractManager.ibcToken()), Error.GA0016_NOT_TOKEN_CONTRACT);
    }

    /**
     * @dev 送信者がTokenコントラクト（tokenのみ）かどうかを検証
     * @param contractManager ContractManager参照
     * @param sender 送信者アドレス
     */
    function requireTokenOnlyContract(IContractManager contractManager, address sender) internal view {
        require(sender == address(contractManager.token()), Error.GA0016_NOT_TOKEN_CONTRACT);
    }

    /**
     * @dev 送信者がAccountコントラクトかどうかを検証
     * @param contractManager ContractManager参照
     * @param sender 送信者アドレス
     */
    function requireAccountContract(IContractManager contractManager, address sender) internal view {
        require(sender == address(contractManager.account()), Error.GA0012_NOT_ACCOUNT_CONTRACT);
    }

    /**
     * @dev 送信者がIBCAppコントラクトかどうかを検証
     * @param contractManager ContractManager参照
     * @param sender 送信者アドレス
     */
    function requireIBCAppContract(IContractManager contractManager, address sender) internal view {
        require(
            sender == address(contractManager.ibcApp(Constant._ACCOUNT_SYNC)),
            Error.GA0022_NOT_IBC_CONTRACT
        );
    }

    /**
     * @dev アカウント存在確認とBusinessZoneAccount存在確認を行う
     * @param contractManager ContractManager参照
     * @param businessZoneAccountStorage BusinessZoneAccountStorage参照
     * @param zoneId ゾーンID
     * @param accountId アカウントID
     */
    function requireAccountAndBizZoneAccountExists(
        IContractManager contractManager,
        IBusinessZoneAccountStorage businessZoneAccountStorage,
        uint16 zoneId,
        bytes32 accountId
    ) internal view {
        // Account存在確認
        (bool success, string memory errTmp) = contractManager.account().hasAccount(accountId);
        require(success, errTmp);

        // businessZoneAccount存在確認
        (bool hasAccount, string memory err) = hasAccountByZone(
            businessZoneAccountStorage,
            zoneId,
            accountId
        );
        require(hasAccount, err);
    }

    ///////////////////////////////////
    // バリデーション関数
    ///////////////////////////////////

    /**
     * @dev setActiveBusinessAccountWithZone時の検証処理
     */
    function setActiveBusinessAccountWithZoneIsValid(
        IContractManager contractManager,
        IBusinessZoneAccountStorage businessZoneAccountStorage,
        uint16 zoneId,
        bytes32 accountId,
        address sender
    ) external view {
        requireValidatorContract(contractManager, sender);
        requireAccountAndBizZoneAccountExists(contractManager, businessZoneAccountStorage, zoneId, accountId);
    }

    /**
     * @dev setBizZoneTerminated時の検証処理
     */
    function setBizZoneTerminatedIsValid(
        IContractManager contractManager,
        IBusinessZoneAccountStorage businessZoneAccountStorage,
        uint16 zoneId,
        bytes32 accountId,
        address sender
    ) external view {
        requireValidatorContract(contractManager, sender);
        requireAccountAndBizZoneAccountExists(contractManager, businessZoneAccountStorage, zoneId, accountId);
    }

    /**
     * @dev syncBusinessZoneStatus時の検証処理
     */
    function syncBusinessZoneStatusIsValid(IContractManager contractManager, address sender)
        external
        view
    {
        requireIBCAppContract(contractManager, sender);
    }

    /**
     * @dev syncBusinessZoneBalance時の検証処理
     */
    function syncBusinessZoneBalanceIsValid(IContractManager contractManager, address sender)
        external
        view
    {
        requireTokenContract(contractManager, sender);
    }

    /**
     * @dev addBusinessZoneBalance時の検証処理
     */
    function addBusinessZoneBalanceIsValid(IContractManager contractManager, address sender)
        external
        view
    {
        requireIBCTokenContract(contractManager, sender);
    }

    /**
     * @dev subtractBusinessZoneBalance時の検証処理
     */
    function subtractBusinessZoneBalanceIsValid(
        IContractManager contractManager,
        IBusinessZoneAccountStorage businessZoneAccountStorage,
        uint16 zoneId,
        bytes32 accountId,
        address sender
    ) external view {
        requireTokenContract(contractManager, sender);

        // 送信元アカウントの登録確認
        (bool success, string memory err) = hasAccountByZone(
            businessZoneAccountStorage,
            zoneId,
            accountId
        );
        require(success, err);
    }

    /**
     * @dev forceBurnAllBalance時の検証処理
     */
    function forceBurnAllBalanceIsValid(IContractManager contractManager, address sender)
        external
        view
    {
        requireAccountContract(contractManager, sender);
    }

    /**
     * @dev balanceUpdateByRedeemVoucher時の検証処理
     */
    function balanceUpdateByRedeemVoucherIsValid(
        IContractManager contractManager,
        IBusinessZoneAccountStorage businessZoneAccountStorage,
        uint16 zoneId,
        bytes32 accountId,
        uint256 amount,
        address sender
    ) external view {
        requireTokenOnlyContract(contractManager, sender);

        // 付加領域からの結果なので失敗しない前提だが、Balanceのチェックを行う
        require(
            businessZoneAccountStorage.getBusinessZoneAccountData(zoneId, accountId).balance >=
                amount,
            Error.UE4402_BALANCE_NOT_ENOUGH
        );
    }

    /**
     * @dev balanceUpdateByIssueVoucher時の検証処理
     */
    function balanceUpdateByIssueVoucherIsValid(IContractManager contractManager, address sender)
        external
        view
    {
        requireTokenContract(contractManager, sender);
    }

    ///////////////////////////////////
    // ビュー関数
    ///////////////////////////////////

    /**
     * @dev BizZoneのAccountId存在確認
     *
     * @param businessZoneAccountStorage BusinessZoneAccountStorage参照
     * @param zoneId ゾーンID
     * @param accountId アカウントID
     * @return success true:存在する,false:存在しない
     * @return err エラーメッセージ
     */
    function hasAccountByZone(
        IBusinessZoneAccountStorage businessZoneAccountStorage,
        uint16 zoneId,
        bytes32 accountId
    ) public view returns (bool success, string memory err) {
        if (zoneId == 0x00) {
            return (false, Error.RV0007_ACCOUNT_INVALID_VAL);
        }
        if (accountId == 0x00) {
            return (false, Error.RV0007_ACCOUNT_INVALID_VAL);
        }
        if (!businessZoneAccountStorage.getAccountIdExistenceByZoneId(zoneId, accountId)) {
            return (false, Error.GE0105_ACCOUNT_ID_NOT_EXIST);
        }

        return (true, "");
    }

    /**
     * @dev BusinessZoneアカウント情報取得
     *
     * @param businessZoneAccountStorage BusinessZoneAccountStorage参照
     * @param zoneId ゾーンID
     * @param accountId アカウントID
     * @return businessZoneAccountData
     */
    function getBusinessZoneAccount(
        IBusinessZoneAccountStorage businessZoneAccountStorage,
        uint16 zoneId,
        bytes32 accountId
    ) external view returns (BusinessZoneAccountData memory) {
        return businessZoneAccountStorage.getBusinessZoneAccountData(zoneId, accountId);
    }

    /**
     * @dev BusinessZoneアカウントのアクティブ状態確認
     *
     * @param businessZoneAccountStorage BusinessZoneAccountStorage参照
     * @param zoneId ゾーンID
     * @param accountId アカウントID
     * @return success true:アクティブ状態,false:アクティブ以外
     * @return err エラーメッセージ
     */
    function isActivatedByZone(
        IBusinessZoneAccountStorage businessZoneAccountStorage,
        uint16 zoneId,
        bytes32 accountId
    ) external view returns (bool success, string memory err) {
        (success, err) = hasAccountByZone(businessZoneAccountStorage, zoneId, accountId);
        if (!success) {
            return (false, err);
        }
        BusinessZoneAccountData memory data = businessZoneAccountStorage.getBusinessZoneAccountData(
            zoneId,
            accountId
        );
        if (data.accountStatus == Constant._STATUS_ACTIVE) {
            return (true, "");
        } else {
            return (false, Error.GE2005_ACCOUNT_DISABLED);
        }
    }

    /**
     * @dev BusinessZoneアカウントの存在確認
     *
     * @param businessZoneAccountStorage BusinessZoneAccountStorage参照
     * @param zoneId ゾーンID
     * @param accountId アカウントID
     * @return success true:存在する,false:存在しない
     */
    function accountIdExistenceByZoneId(
        IBusinessZoneAccountStorage businessZoneAccountStorage,
        uint16 zoneId,
        bytes32 accountId
    ) external view returns (bool success) {
        return businessZoneAccountStorage.getAccountIdExistenceByZoneId(zoneId, accountId);
    }

    /**
     * @dev limitとoffsetで指定したBusinessZoneAccountsを一括取得する
     *
     * @param businessZoneAccountStorage BusinessZoneAccountStorage参照
     * @param index オフセット
     * @return bizAccounts 全BusinessZoneAccountsの情報
     */
    function getBizAccountsAll(
        IBusinessZoneAccountStorage businessZoneAccountStorage,
        uint256 index
    ) external view returns (BizAccountsAll memory bizAccounts) {
        return businessZoneAccountStorage.getBizAccountsAll(index);
    }
}
