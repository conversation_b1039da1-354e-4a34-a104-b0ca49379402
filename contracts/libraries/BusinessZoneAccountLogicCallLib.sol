// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;

import "../interfaces/IContractManager.sol";
import "../interfaces/IBusinessZoneAccountStorage.sol";
import "../interfaces/Struct.sol";
import "../interfaces/Error.sol";

/**
 * @dev BusinessZoneAccountLogicCallLibライブラリ
 *      BusinessZoneAccountのview/pure関数を実装するヘルパーライブラリ
 *      ブロックチェーンデータの変更は行わない
 */
library BusinessZoneAccountLogicCallLib {
    /** @dev 未登録の場合にて返す空の値 */
    bytes32 private constant EMPTY_VALUE = 0x00;

    ///////////////////////////////////
    // バリデーション関数
    ///////////////////////////////////

    /**
     * @dev setBusinessZoneAccountStatus時の検証処理
     *
     * @param contractManager ContractManager参照
     * @param businessZoneAccountStorage BusinessZoneAccountStorage参照
     * @param zoneId ゾーンID
     * @param accountId アカウントID
     * @param sender 送信者アドレス
     */
    function setBusinessZoneAccountStatusIsValid(
        IContractManager contractManager,
        IBusinessZoneAccountStorage businessZoneAccountStorage,
        uint16 zoneId,
        bytes32 accountId,
        address sender
    ) external view {
        // validatorコントラクトからの呼び出しである事が条件
        require(
            sender == address(contractManager.validator()),
            Error.GA0019_NOT_VALIDATOR_CONTRACT
        );

        //Account存在確認
        (bool success, string memory errTmp) = contractManager.account().hasAccount(accountId);
        require(success, errTmp);

        // businessZoneAccountが既に登録されている＝申し込みが完了している ことを条件とする
        (bool hasAccount, string memory err) = hasAccountByZone(
            businessZoneAccountStorage,
            zoneId,
            accountId
        );
        require(hasAccount, err);
    }

    /**
     * @dev syncBusinessZoneStatus時の検証処理
     *
     * @param contractManager ContractManager参照
     * @param sender 送信者アドレス
     */
    function syncBusinessZoneStatusIsValid(IContractManager contractManager, address sender)
        external
        view
    {
        //IBCコントラクトからの呼び出しが条件
        require(
            sender == address(contractManager.ibcApp(Constant._ACCOUNT_SYNC)),
            Error.GA0022_NOT_IBC_CONTRACT
        );
    }

    /**
     * @dev addBusinessZoneBalance時の検証処理
     *
     * @param contractManager ContractManager参照
     * @param sender 送信者アドレス
     */
    function addBusinessZoneBalanceIsValid(IContractManager contractManager, address sender)
        external
        view
    {
        // Tokenコントラクトからの呼び出しであることが条件
        require(sender == address(contractManager.ibcToken()), Error.GA0016_NOT_TOKEN_CONTRACT);
    }

    /**
     * @dev subtractBusinessZoneBalance時の検証処理
     *
     * @param contractManager ContractManager参照
     * @param businessZoneAccountStorage BusinessZoneAccountStorage参照
     * @param zoneId ゾーンID
     * @param accountId アカウントID
     * @param sender 送信者アドレス
     */
    function subtractBusinessZoneBalanceIsValid(
        IContractManager contractManager,
        IBusinessZoneAccountStorage businessZoneAccountStorage,
        uint16 zoneId,
        bytes32 accountId,
        address sender
    ) external view {
        // Tokenコントラクトからの呼び出しであることが条件
        requireTokenContract(contractManager, sender);

        // 送信元アカウントの登録確認
        (bool success, string memory err) = hasAccountByZone(
            businessZoneAccountStorage,
            zoneId,
            accountId
        );
        require(success, err);
    }

    /**
     * @dev forceBurnAllBalance時の検証処理
     *
     * @param contractManager ContractManager参照
     * @param sender 送信者アドレス
     */
    function forceBurnAllBalanceIsValid(IContractManager contractManager, address sender)
        external
        view
    {
        // Accountコントラクトからの呼び出しであることが条件
        require(sender == address(contractManager.account()), Error.GA0012_NOT_ACCOUNT_CONTRACT);
    }

    /**
     * @dev balanceUpdateByRedeemVoucher時の検証処理
     *
     * @param contractManager ContractManager参照
     * @param sender 送信者アドレス
     */
    function balanceUpdateByRedeemVoucherIsValid(
        IContractManager contractManager,
        IBusinessZoneAccountStorage businessZoneAccountStorage,
        uint16 zoneId,
        bytes32 accountId,
        uint256 amount,
        address sender
    ) external view {
        // Tokenコントラクトからの呼び出しであることが条件
        require(sender == address(contractManager.token()), Error.GA0016_NOT_TOKEN_CONTRACT);
        // 付加領域からの結果なので失敗しない前提だが、Balanceのチェックを行う
        BusinessZoneAccountData memory accountData = businessZoneAccountStorage
            .getBusinessZoneAccountData(zoneId, accountId);
        require(accountData.balance >= amount, Error.UE4402_BALANCE_NOT_ENOUGH);
    }

    /**
     * @dev requireTokenContract
     *
     * @param contractManager ContractManager参照
     * @param sender 送信者アドレス
     */
    function requireTokenContract(IContractManager contractManager, address sender) external view {
        require(
            (sender == address(contractManager.token()) ||
                sender == address(contractManager.ibcToken())),
            Error.GA0016_NOT_TOKEN_CONTRACT
        );
    }

    /**
     * @dev BizZoneのAccountId存在確認
     *
     * @param businessZoneAccountStorage BusinessZoneAccountStorage参照
     * @param zoneId ゾーンID
     * @param accountId アカウントID
     * @return success true:存在する,false:存在しない
     * @return err エラーメッセージ
     */
    function hasAccountByZone(
        IBusinessZoneAccountStorage businessZoneAccountStorage,
        uint16 zoneId,
        bytes32 accountId
    ) public view returns (bool success, string memory err) {
        if (zoneId == 0x00) {
            return (false, Error.RV0007_ACCOUNT_INVALID_VAL);
        }
        if (accountId == 0x00) {
            return (false, Error.RV0007_ACCOUNT_INVALID_VAL);
        }
        if (!businessZoneAccountStorage.getAccountIdExistenceByZoneId(zoneId, accountId)) {
            return (false, Error.GE0105_ACCOUNT_ID_NOT_EXIST);
        }

        return (true, "");
    }

    /**
     * @dev BusinessZoneアカウント情報取得
     *
     * @param businessZoneAccountStorage BusinessZoneAccountStorage参照
     * @param zoneId ゾーンID
     * @param accountId アカウントID
     * @return businessZoneAccountData
     */
    function getBusinessZoneAccount(
        IBusinessZoneAccountStorage businessZoneAccountStorage,
        uint16 zoneId,
        bytes32 accountId
    ) external view returns (BusinessZoneAccountData memory) {
        return businessZoneAccountStorage.getBusinessZoneAccountData(zoneId, accountId);
    }

    /**
     * @dev BusinessZoneアカウントのアクティブ状態確認
     *
     * @param businessZoneAccountStorage BusinessZoneAccountStorage参照
     * @param zoneId ゾーンID
     * @param accountId アカウントID
     * @return success true:アクティブ状態,false:アクティブ以外
     * @return err エラーメッセージ
     */
    function isActivatedByZone(
        IBusinessZoneAccountStorage businessZoneAccountStorage,
        uint16 zoneId,
        bytes32 accountId
    ) external view returns (bool success, string memory err) {
        (success, err) = hasAccountByZone(businessZoneAccountStorage, zoneId, accountId);
        if (!success) {
            return (false, err);
        }
        BusinessZoneAccountData memory data = businessZoneAccountStorage.getBusinessZoneAccountData(
            zoneId,
            accountId
        );
        if (data.accountStatus == Constant._STATUS_ACTIVE) {
            return (true, "");
        } else {
            return (false, Error.GE2005_ACCOUNT_DISABLED);
        }
    }

    /**
     * @dev BusinessZoneアカウントの存在確認
     *
     * @param businessZoneAccountStorage BusinessZoneAccountStorage参照
     * @param zoneId ゾーンID
     * @param accountId アカウントID
     * @return success true:存在する,false:存在しない
     */
    function accountIdExistenceByZoneId(
        IBusinessZoneAccountStorage businessZoneAccountStorage,
        uint16 zoneId,
        bytes32 accountId
    ) external view returns (bool success) {
        return businessZoneAccountStorage.getAccountIdExistenceByZoneId(zoneId, accountId);
    }

    /**
     * @dev limitとoffsetで指定したBusinessZoneAccountsを一括取得する
     *
     * @param businessZoneAccountStorage BusinessZoneAccountStorage参照
     * @param index オフセット
     * @return bizAccounts 全BusinessZoneAccountsの情報
     */
    function getBizAccountsAll(
        IBusinessZoneAccountStorage businessZoneAccountStorage,
        uint256 index
    ) external view returns (BizAccountsAll memory bizAccounts) {
        return businessZoneAccountStorage.getBizAccountsAll(index);
    }
}
