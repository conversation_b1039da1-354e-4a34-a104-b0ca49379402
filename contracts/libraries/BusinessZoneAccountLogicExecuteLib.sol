// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;

import "../interfaces/Error.sol";
import "../interfaces/IBusinessZoneAccountStorage.sol";
import "../interfaces/IContractManager.sol";
import "../interfaces/Struct.sol";

/**
 * @dev BusinessZoneAccountLogicExecuteLibライブラリ
 *      BusinessZoneAccountの実行関数を実装するヘルパーライブラリ
 */
library BusinessZoneAccountLogicExecuteLib {
    /**
     * @dev BusinessZoneアカウント追加の実行処理
     *
     * @param businessZoneAccountStorage BusinessZoneAccountStorage参照
     * @param zoneId ゾーンID
     * @param accountId アカウントID
     */
    function executeSetActiveBusinessAccountWithZone(
        IBusinessZoneAccountStorage businessZoneAccountStorage,
        uint16 zoneId,
        bytes32 accountId
    ) external {
        BusinessZoneAccountData memory accountData = businessZoneAccountStorage
            .getBusinessZoneAccountData(zoneId, accountId);
        accountData.accountStatus = Constant._STATUS_ACTIVE;
        accountData.registeredAt = block.timestamp;
        businessZoneAccountStorage.setBusinessZoneAccountData(zoneId, accountId, accountData);
    }

    /**
     * @dev BusinessZoneアカウント解約の実行処理
     *
     * @param businessZoneAccountStorage BusinessZoneAccountStorage参照
     * @param zoneId ゾーンID
     * @param accountId アカウントID
     */
    function executeSetBizZoneTerminated(
        IBusinessZoneAccountStorage businessZoneAccountStorage,
        uint16 zoneId,
        bytes32 accountId
    ) external {
        BusinessZoneAccountData memory accountData = businessZoneAccountStorage
            .getBusinessZoneAccountData(zoneId, accountId);
        accountData.accountStatus = Constant._STATUS_TERMINATED;
        accountData.terminatedAt = block.timestamp;
        businessZoneAccountStorage.setBusinessZoneAccountData(zoneId, accountId, accountData);
    }

    /**
     * @dev ビジネスゾーンアカウントステータス更新申し込みの実行処理
     *
     * @param contractManager ContractManager参照
     * @param businessZoneAccountStorage BusinessZoneAccountStorage参照
     * @param zoneId zoneId
     * @param accountId アカウントID
     * @param accountName アカウント名
     * @param accountStatus アカウントステータス
     * @param traceId トレースID
     * @return validatorId バリデーターID
     */
    function executeSyncBusinessZoneStatus(
        IContractManager contractManager,
        IBusinessZoneAccountStorage businessZoneAccountStorage,
        uint16 zoneId,
        bytes32 accountId,
        string memory accountName,
        bytes32 accountStatus,
        bytes32 traceId
    ) external returns (bytes32 validatorId) {
        if (
            accountStatus == Constant._STATUS_APPLIYNG &&
            businessZoneAccountStorage.getAccountIdExistenceByZoneId(zoneId, accountId)
        ) {
            BusinessZoneAccountData memory accountData = businessZoneAccountStorage
                .getBusinessZoneAccountData(zoneId, accountId);
            accountData.accountStatus = accountStatus;
            businessZoneAccountStorage.setBusinessZoneAccountData(zoneId, accountId, accountData);
        } else {
            // ビジネスゾーンアカウント情報を同期する（ビジネスロジック）
            BusinessZoneAccountData memory accountData = businessZoneAccountStorage
                .getBusinessZoneAccountData(zoneId, accountId);
            accountData.accountStatus = accountStatus;
            if (accountStatus == Constant._STATUS_APPLIYNG) {
                accountData = BusinessZoneAccountData({
                    accountName: accountName,
                    balance: 0,
                    accountStatus: accountStatus,
                    appliedAt: block.timestamp,
                    registeredAt: 0,
                    terminatingAt: 0,
                    terminatedAt: 0
                });
            } else if (accountStatus == Constant._STATUS_TERMINATING) {
                accountData.accountStatus = accountStatus;
                accountData.terminatingAt = block.timestamp;
            }

            businessZoneAccountStorage.setBusinessZoneAccountData(zoneId, accountId, accountData);
            businessZoneAccountStorage.setAccountIdExistenceByZoneId(zoneId, accountId, true);
        }

        (validatorId, ) = contractManager.account().getValidatorIdByAccountId(accountId);
    }

    /**
     * @dev ビジネスゾーンアカウント残高更新の実行処理
     *
     * @param contractManager ContractManager参照
     * @param businessZoneAccountStorage BusinessZoneAccountStorage参照
     * @param params BizZone内送金の残高更新のデータ
     * @return transferData 転送データ
     */
    function executeSyncBusinessZoneBalance(
        IContractManager contractManager,
        IBusinessZoneAccountStorage businessZoneAccountStorage,
        SyncBuisinessZoneBlanaceParams memory params
    ) external returns (TransferData memory transferData) {
        // ビジネスゾーン残高を同期する（ビジネスロジック）
        BusinessZoneAccountData memory toAccountData = businessZoneAccountStorage
            .getBusinessZoneAccountData(params.fromZoneId, params.toAccountId);
        BusinessZoneAccountData memory fromAccountData = businessZoneAccountStorage
            .getBusinessZoneAccountData(params.fromZoneId, params.fromAccountId);

        toAccountData.balance += params.amount;
        fromAccountData.balance -= params.amount;

        businessZoneAccountStorage.setBusinessZoneAccountData(
            params.fromZoneId,
            params.toAccountId,
            toAccountData
        );
        businessZoneAccountStorage.setBusinessZoneAccountData(
            params.fromZoneId,
            params.fromAccountId,
            fromAccountData
        );

        // fromAccountIdからフィルタリング用のvalidatorIdを取得
        (bytes32 fromValidatorId, ) = contractManager.account().getValidatorIdByAccountId(
            params.fromAccountId
        );

        (bytes32 toValidatorId, ) = contractManager.account().getValidatorIdByAccountId(
            params.toAccountId
        );

        return
            TransferData({
                transferType: Constant._TRANSFER,
                zoneId: params.fromZoneId,
                fromValidatorId: fromValidatorId,
                toValidatorId: toValidatorId,
                fromAccountBalance: fromAccountData.balance,
                toAccountBalance: toAccountData.balance,
                businessZoneBalance: Constant._EMPTY_LENGTH,
                bizZoneId: Constant._EMPTY_UINT16,
                sendAccountId: params.fromAccountId,
                fromAccountId: params.fromAccountId,
                fromAccountName: params.fromAccountName,
                toAccountId: params.toAccountId,
                toAccountName: params.toAccountName,
                amount: params.amount,
                miscValue1: Constant._EMPTY_VALUE,
                miscValue2: Constant._EMPTY_STRING,
                memo: ""
            });
    }

    /**
     * @dev ビジネスゾーン残高チャージの実行処理
     *
     * @param businessZoneAccountStorage BusinessZoneAccountStorage参照
     * @param zoneId zoneId
     * @param accountId 送信先アカウントID
     * @param amount チャージ額
     */
    function executeAddBusinessZoneBalance(
        IBusinessZoneAccountStorage businessZoneAccountStorage,
        uint16 zoneId,
        bytes32 accountId,
        uint256 amount
    ) external {
        businessZoneAccountStorage.addAccountBalance(zoneId, accountId, amount);
    }

    /**
     * @dev ビジネスゾーン残高ディスチャージの実行処理
     *
     * @param businessZoneAccountStorage BusinessZoneAccountStorage参照
     * @param zoneId zoneId
     * @param accountId 送信先アカウントID
     * @param amount チャージ額
     */
    function executeSubtractBusinessZoneBalance(
        IBusinessZoneAccountStorage businessZoneAccountStorage,
        uint16 zoneId,
        bytes32 accountId,
        uint256 amount
    ) external {
        businessZoneAccountStorage.subtractAccountBalance(zoneId, accountId, amount);
    }

    /**
     * @dev 全てのビジネスゾーンの残高を強制償却する実行処理
     *
     * @param contractManager ContractManager参照
     * @param businessZoneAccountStorage BusinessZoneAccountStorage参照
     * @param accountId アカウントID
     * @return burnedAmount 償却した数量
     * @return forceDischarge ディスチャージしたBizゾーン情報
     */
    function executeForceBurnAllBalance(
        IContractManager contractManager,
        IBusinessZoneAccountStorage businessZoneAccountStorage,
        bytes32 accountId
    ) external returns (uint256 burnedAmount, ForceDischarge[] memory forceDischarge) {
        ZoneData[] memory zones = contractManager.account().getZoneByAccountId(accountId);
        forceDischarge = new ForceDischarge[](zones.length);
        for (uint256 i = 0; i < zones.length; i++) {
            uint16 zoneId = zones[i].zoneId;
            // 指定のゾーンの残高を強制償却する（ビジネスロジック）
            BusinessZoneAccountData memory accountData = businessZoneAccountStorage
                .getBusinessZoneAccountData(zoneId, accountId);
            uint256 dischargeAmount = accountData.balance;
            accountData.balance = 0;
            accountData.accountStatus = Constant._STATUS_FORCE_BURNED;
            businessZoneAccountStorage.setBusinessZoneAccountData(zoneId, accountId, accountData);
            burnedAmount += dischargeAmount;
            forceDischarge[i].zoneId = zoneId;
            forceDischarge[i].dischargeAmount = dischargeAmount;
        }
        return (burnedAmount, forceDischarge);
    }

    /**
     * @dev 残高を更新(償却)の実行処理
     *
     * @param businessZoneAccountStorage BusinessZoneAccountStorage参照
     * @param zoneId zoneId
     * @param accountId accountId
     * @param amount Burnする数量
     */
    function executeBalanceUpdateByRedeemVoucher(
        IBusinessZoneAccountStorage businessZoneAccountStorage,
        uint16 zoneId,
        bytes32 accountId,
        uint256 amount
    ) external {
        businessZoneAccountStorage.subtractAccountBalance(zoneId, accountId, amount);
    }

    /**
     * @dev 残高を更新(発行)の実行処理
     *
     * @param businessZoneAccountStorage BusinessZoneAccountStorage参照
     * @param zoneId zoneId
     * @param accountId accountId
     * @param amount Burnする数量
     */
    function executeBalanceUpdateByIssueVoucher(
        IBusinessZoneAccountStorage businessZoneAccountStorage,
        uint16 zoneId,
        bytes32 accountId,
        uint256 amount
    ) external {
        businessZoneAccountStorage.addAccountBalance(zoneId, accountId, amount);
    }

    /**
     * @dev 指定されたAccountIdsに紐づくBusinessZoneAccounts情報を登録、もしくは上書きする実行処理
     *
     * @param businessZoneAccountStorage BusinessZoneAccountStorage参照
     * @param bizAccounts bizAccountInfo
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature Adminユーザによる署名
     */
    function executeSetBizAccountsAll(
        IBusinessZoneAccountStorage businessZoneAccountStorage,
        BizAccountsAll memory bizAccounts,
        uint256 deadline,
        bytes memory signature
    ) external {
        businessZoneAccountStorage.setBizAccountsAll(bizAccounts, deadline, signature);
    }
}
