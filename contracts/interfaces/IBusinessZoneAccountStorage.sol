// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;
pragma abicoder v2;

import "./Struct.sol";

/**
 * @dev IBusinessZoneAccountStorageインターフェース
 *      BusinessZoneAccountデータのストレージ操作を定義
 *      BusinessZoneAccountLogicコントラクトからのみ呼び出し可能
 */
interface IBusinessZoneAccountStorage {
    ///////////////////////////////////
    // BusinessZoneAccountData CRUD操作
    ///////////////////////////////////

    /**
     * @dev BusinessZoneAccountデータを取得する
     * @param zoneId ゾーンID
     * @param accountId アカウントID
     * @return businessZoneAccountData BusinessZoneAccountデータ
     */
    function getBusinessZoneAccountData(uint16 zoneId, bytes32 accountId)
        external
        view
        returns (BusinessZoneAccountData memory businessZoneAccountData);

    /**
     * @dev BusinessZoneAccountデータを設定する
     * @param zoneId ゾーンID
     * @param accountId アカウントID
     * @param businessZoneAccountData BusinessZoneAccountデータ
     */
    function setBusinessZoneAccountData(
        uint16 zoneId,
        bytes32 accountId,
        BusinessZoneAccountData memory businessZoneAccountData
    ) external;

    /**
     * @dev アカウントステータスを更新する
     * @param zoneId ゾーンID
     * @param accountId アカウントID
     * @param accountStatus アカウントステータス
     */
    function updateAccountStatus(
        uint16 zoneId,
        bytes32 accountId,
        bytes32 accountStatus
    ) external;

    /**
     * @dev アカウント名を更新する
     * @param zoneId ゾーンID
     * @param accountId アカウントID
     * @param accountName アカウント名
     */
    function updateAccountName(
        uint16 zoneId,
        bytes32 accountId,
        string memory accountName
    ) external;

    /**
     * @dev 申込日時を更新する
     * @param zoneId ゾーンID
     * @param accountId アカウントID
     * @param appliedAt 申込日時
     */
    function updateAppliedAt(
        uint16 zoneId,
        bytes32 accountId,
        uint256 appliedAt
    ) external;

    /**
     * @dev 登録日時を更新する
     * @param zoneId ゾーンID
     * @param accountId アカウントID
     * @param registeredAt 登録日時
     */
    function updateRegisteredAt(
        uint16 zoneId,
        bytes32 accountId,
        uint256 registeredAt
    ) external;

    /**
     * @dev 解約申込日時を更新する
     * @param zoneId ゾーンID
     * @param accountId アカウントID
     * @param terminatingAt 解約申込日時
     */
    function updateTerminatingAt(
        uint16 zoneId,
        bytes32 accountId,
        uint256 terminatingAt
    ) external;

    /**
     * @dev 解約日時を更新する
     * @param zoneId ゾーンID
     * @param accountId アカウントID
     * @param terminatedAt 解約日時
     */
    function updateTerminatedAt(
        uint16 zoneId,
        bytes32 accountId,
        uint256 terminatedAt
    ) external;

    /**
     * @dev 残高を更新する
     * @param zoneId ゾーンID
     * @param accountId アカウントID
     * @param balance 新しい残高
     */
    function updateBalance(
        uint16 zoneId,
        bytes32 accountId,
        uint256 balance
    ) external;

    /**
     * @dev 残高を増加させる
     * @param zoneId ゾーンID
     * @param accountId アカウントID
     * @param amount 増加金額
     */
    function increaseBalance(
        uint16 zoneId,
        bytes32 accountId,
        uint256 amount
    ) external;

    /**
     * @dev 残高を減少させる
     * @param zoneId ゾーンID
     * @param accountId アカウントID
     * @param amount 減少金額
     */
    function decreaseBalance(
        uint16 zoneId,
        bytes32 accountId,
        uint256 amount
    ) external;

    ///////////////////////////////////
    // アカウント存在確認管理
    ///////////////////////////////////

    /**
     * @dev アカウント存在確認フラグを取得する
     * @param zoneId ゾーンID
     * @param accountId アカウントID
     * @return exists 存在確認フラグ
     */
    function getAccountIdExistenceByZoneId(uint16 zoneId, bytes32 accountId)
        external
        view
        returns (bool exists);

    /**
     * @dev アカウント存在確認フラグを設定する
     * @param zoneId ゾーンID
     * @param accountId アカウントID
     * @param exists 存在確認フラグ
     */
    function setAccountIdExistenceByZoneId(
        uint16 zoneId,
        bytes32 accountId,
        bool exists
    ) external;

    ///////////////////////////////////
    // バックアップ・リストア用
    ///////////////////////////////////

    /**
     * @dev 全BusinessZoneAccountデータを設定する（バックアップ・リストア用）
     * @param bizAccounts 全BusinessZoneAccountデータ
     * @param deadline 署名期限
     * @param signature 署名
     */
    function setBizAccountsAll(
        BizAccountsAll memory bizAccounts,
        uint256 deadline,
        bytes memory signature
    ) external;

    /**
     * @dev 全BusinessZoneAccountデータを取得する（バックアップ・リストア用）
     * @param index インデックス
     * @return bizAccounts 全BusinessZoneAccountデータ
     */
    function getBizAccountsAll(uint256 index)
        external
        view
        returns (BizAccountsAll memory bizAccounts);
}
