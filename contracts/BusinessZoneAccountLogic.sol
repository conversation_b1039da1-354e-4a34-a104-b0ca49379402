// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;
pragma abicoder v2;

import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";
import "@openzeppelin/contracts-upgradeable/utils/math/SafeMathUpgradeable.sol";

import "./interfaces/IContractManager.sol";
import "./interfaces/IBusinessZoneAccountStorage.sol";
import "./interfaces/IBusinessZoneAccount.sol";
import "./interfaces/Error.sol";
import "./interfaces/Struct.sol";

// ライブラリのimport
import "./libraries/BusinessZoneAccountLogicCallLib.sol";
import "./libraries/BusinessZoneAccountLogicExecuteLib.sol";

/**
 * @dev BusinessZoneAccountLogicコントラクト(FinZone用)
 */
contract BusinessZoneAccountLogic is Initializable, IBusinessZoneAccount {
    ///////////////////////////////////
    // private variables
    ///////////////////////////////////

    /** @dev ContractManagerアドレス */
    IContractManager private _contractManager;

    /** @dev BusinessZoneAccountStorageアドレス */
    IBusinessZoneAccountStorage private _businessZoneAccountStorage;

    ///////////////////////////////////
    // functions
    ///////////////////////////////////

    /**
     * @dev Upgrades Pluginsでのdeploy後に呼び出される。通常のコンストラクタ相当。
     *
     * @param contractManager contractManager
     * @param businessZoneAccountStorage businessZoneAccountStorage
     */
    function initialize(
        IContractManager contractManager,
        IBusinessZoneAccountStorage businessZoneAccountStorage
    ) public initializer {
        require(
            address(contractManager) != address(0) &&
                address(businessZoneAccountStorage) != address(0),
            Error.GE0114_INVALID_CONTRACT_ADDRESS
        );
        _contractManager = contractManager;
        _businessZoneAccountStorage = businessZoneAccountStorage;
    }

    /**
     * @dev コントラクトバージョン取得。
     * @return version コントラクトバージョン
     */
    function version() external pure returns (string memory) {
        return "v1";
    }

    ///////////////////////////////////
    // send functions
    ///////////////////////////////////

    /**
     * @dev BusinessZoneアカウント追加(CoreAPI/industry用)
     *
     * @param zoneId ゾーンID
     * @param accountId アカウントID
     * @param traceId トレースID
     */
    function setActiveBusinessAccountWithZone(
        uint16 zoneId,
        bytes32 accountId,
        bytes32 traceId
    ) external override {
        BusinessZoneAccountLogicCallLib.setBusinessZoneAccountStatusIsValid(
            _contractManager,
            _businessZoneAccountStorage,
            zoneId,
            accountId,
            msg.sender
        );

        BusinessZoneAccountLogicExecuteLib.executeSetActiveBusinessAccountWithZone(
            _businessZoneAccountStorage,
            zoneId,
            accountId
        );

        emit SetActiveBusinessAccountWithZone(accountId, zoneId, traceId);
    }

    /**
     * @dev BusinessZoneアカウント解約
     *
     * @param zoneId ゾーンID
     * @param accountId アカウントID
     */
    function setBizZoneTerminated(uint16 zoneId, bytes32 accountId) external override {
        BusinessZoneAccountLogicCallLib.setBusinessZoneAccountStatusIsValid(
            _contractManager,
            _businessZoneAccountStorage,
            zoneId,
            accountId,
            msg.sender
        );

        BusinessZoneAccountLogicExecuteLib.executeSetBizZoneTerminated(
            _businessZoneAccountStorage,
            zoneId,
            accountId
        );
    }

    /**
     * @dev ビジネスゾーンアカウントステータス更新申し込み(BizZoneアカウント申し込み含む)
     * ```
     * emit event: SyncBusinessZoneStatus()
     * ```
     * @param zoneId zoneId
     * @param zoneName zone名
     * @param accountId アカウントID
     * @param accountName アカウント名
     * @param accountStatus アカウントステータス
     * @param traceId トレースID
     */
    function syncBusinessZoneStatus(
        uint16 zoneId,
        string memory zoneName,
        bytes32 accountId,
        string memory accountName,
        bytes32 accountStatus,
        bytes32 traceId
    ) external override {
        BusinessZoneAccountLogicCallLib.syncBusinessZoneStatusIsValid(_contractManager, msg.sender);

        bytes32 validatorId = BusinessZoneAccountLogicExecuteLib.executeSyncBusinessZoneStatus(
            _contractManager,
            _businessZoneAccountStorage,
            zoneId,
            accountId,
            accountName,
            accountStatus,
            traceId
        );

        emit SyncBusinessZoneStatus(
            validatorId,
            accountId,
            zoneId,
            zoneName,
            accountStatus,
            traceId
        );
    }

    /**
     * @dev ビジネスゾーンアカウントステータス更新
     * ```
     * emit event: SyncBusinessZoneBalance()
     * ```
     *
     * @param params BizZone内送金の残高更新のデータ
     */
    function syncBusinessZoneBalance(SyncBuisinessZoneBlanaceParams memory params)
        external
        override
    {
        BusinessZoneAccountLogicCallLib.requireTokenContract(_contractManager, msg.sender);

        TransferData memory transferData = BusinessZoneAccountLogicExecuteLib
            .executeSyncBusinessZoneBalance(_contractManager, _businessZoneAccountStorage, params);

        emit SyncBusinessZoneBalance(transferData, params.traceId);

        // 取引後残高連携
        _contractManager.account().emitAfterBalance(
            params.fromAccountId,
            params.toAccountId,
            params.traceId
        );
    }

    /**
     * @dev ビジネスゾーン残高チャージ
     *
     * @param zoneId zoneId
     * @param accountId 送信先アカウントID
     * @param amount チャージ額
     */
    function addBusinessZoneBalance(
        uint16 zoneId,
        bytes32 accountId,
        uint256 amount
    ) external override {
        BusinessZoneAccountLogicCallLib.addBusinessZoneBalanceIsValid(_contractManager, msg.sender);

        BusinessZoneAccountLogicExecuteLib.executeAddBusinessZoneBalance(
            _businessZoneAccountStorage,
            zoneId,
            accountId,
            amount
        );
    }

    /**
     * @dev ビジネスゾーン残高ディスチャージ
     *
     * @param zoneId zoneId
     * @param accountId 送信先アカウントID
     * @param amount チャージ額
     */
    function subtractBusinessZoneBalance(
        uint16 zoneId,
        bytes32 accountId,
        uint256 amount
    ) external override {
        BusinessZoneAccountLogicCallLib.subtractBusinessZoneBalanceIsValid(
            _contractManager,
            _businessZoneAccountStorage,
            zoneId,
            accountId,
            msg.sender
        );

        BusinessZoneAccountLogicExecuteLib.executeSubtractBusinessZoneBalance(
            _businessZoneAccountStorage,
            zoneId,
            accountId,
            amount
        );
    }

    /**
     * @dev 全てのビジネスゾーンの残高を強制償却する
     * ゾーンの一覧をAccountコントラクトから取得し、取得した各ゾーンごとに以下の処理を行う
     * 各ゾーンの残高をbalanceUpdateByForceBurnを実行して0に更新する
     * 対象ゾーンの残高が0の場合はスキップする
     *
     *
     * @param accountId アカウントID
     * @return burnedAmount 償却した数量
     * @return forceDischarge ディスチャージしたBizゾーン情報
     */
    function forceBurnAllBalance(bytes32 accountId)
        external
        override
        returns (uint256 burnedAmount, ForceDischarge[] memory forceDischarge)
    {
        BusinessZoneAccountLogicCallLib.forceBurnAllBalanceIsValid(_contractManager, msg.sender);

        return
            BusinessZoneAccountLogicExecuteLib.executeForceBurnAllBalance(
                _contractManager,
                _businessZoneAccountStorage,
                accountId
            );
    }

    /**
     * @dev 残高を更新(償却)
     *
     * @param zoneId zoneId
     * @param accountId accountId
     * @param amount Burnする数量
     * @param traceId トレースID
     */
    function balanceUpdateByRedeemVoucher(
        uint16 zoneId,
        bytes32 accountId,
        uint256 amount,
        bytes32 traceId
    ) external override {
        BusinessZoneAccountLogicCallLib.balanceUpdateByRedeemVoucherIsValid(_contractManager, _businessZoneAccountStorage, zoneId, accountId, amount, msg.sender);

        BusinessZoneAccountLogicExecuteLib.executeBalanceUpdateByRedeemVoucher(
            _businessZoneAccountStorage,
            zoneId,
            accountId,
            amount
        );

        emit BalanceUpdateByRedeemVoucher(zoneId, accountId, amount, traceId);
    }

    /**
     * @dev 残高を更新(発行)
     * @param zoneId zoneId
     * @param accountId accountId
     * @param amount Burnする数量
     * @param traceId トレースID
     */
    function balanceUpdateByIssueVoucher(
        uint16 zoneId,
        bytes32 accountId,
        uint256 amount,
        bytes32 traceId
    ) external override {
        BusinessZoneAccountLogicCallLib.requireTokenContract(_contractManager, msg.sender);

        BusinessZoneAccountLogicExecuteLib.executeBalanceUpdateByIssueVoucher(
            _businessZoneAccountStorage,
            zoneId,
            accountId,
            amount
        );

        emit BalanceUpdateByIssueVoucher(zoneId, accountId, amount, traceId);
    }

    /**
     * @dev 指定されたAccountIdsに紐づくBusinessZoneAccounts情報を登録、もしくは上書きする
     *      バックアップリスト作業時のみ実行
     *
     * @param bizAccounts bizAccountInfo
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature Adminユーザによる署名
     */
    function setBizAccountsAll(
        BizAccountsAll memory bizAccounts,
        uint256 deadline,
        bytes memory signature
    ) external {
        BusinessZoneAccountLogicExecuteLib.executeSetBizAccountsAll(
            _businessZoneAccountStorage,
            bizAccounts,
            deadline,
            signature
        );
    }

    ///////////////////////////////////
    // call functions
    ///////////////////////////////////

    /**
     * @dev BizZoneのAccountId存在確認
     *
     * @param zoneId ゾーンID
     * @param accountId アカウントID
     * @return success true:存在する,false:存在しない
     * @return err エラーメッセージ
     */
    function hasAccountByZone(uint16 zoneId, bytes32 accountId)
        external
        view
        returns (bool success, string memory err)
    {
        return
            BusinessZoneAccountLogicCallLib.hasAccountByZone(
                _businessZoneAccountStorage,
                zoneId,
                accountId
            );
    }

    /**
     * @dev BusinessZoneアカウント情報取得
     *
     * @param zoneId ゾーンID
     * @param accountId アカウントID
     * @return businessZoneAccountData
     */
    function getBusinessZoneAccount(uint16 zoneId, bytes32 accountId)
        external
        view
        returns (BusinessZoneAccountData memory)
    {
        return
            BusinessZoneAccountLogicCallLib.getBusinessZoneAccount(
                _businessZoneAccountStorage,
                zoneId,
                accountId
            );
    }

    /**
     * @dev BusinessZoneアカウントのアクティブ状態確認
     *
     * @param zoneId ゾーンID
     * @param accountId アカウントID
     * @return success true:アクティブ状態,false:アクティブ以外
     */
    function isActivatedByZone(uint16 zoneId, bytes32 accountId)
        external
        view
        returns (bool success, string memory err)
    {
        return
            BusinessZoneAccountLogicCallLib.isActivatedByZone(
                _businessZoneAccountStorage,
                zoneId,
                accountId
            );
    }

    /**
     * @dev BusinessZoneアカウントの存在確認
     *
     * @param zoneId ゾーンID
     * @param accountId アカウントID
     * @return success true:アクティブ状態,false:アクティブ以外
     */
    function accountIdExistenceByZoneId(uint16 zoneId, bytes32 accountId)
        external
        view
        returns (bool success)
    {
        return
            BusinessZoneAccountLogicCallLib.accountIdExistenceByZoneId(
                _businessZoneAccountStorage,
                zoneId,
                accountId
            );
    }

    /**
     * @dev limitとoffsetで指定したBusinessZoneAccountsを一括取得する
     *
     * @param index オフセット
     * @return bizAccounts 全BusinessZoneAccountsの情報
     */
    function getBizAccountsAll(uint256 index)
        external
        view
        override
        returns (BizAccountsAll memory bizAccounts)
    {
        return
            BusinessZoneAccountLogicCallLib.getBizAccountsAll(_businessZoneAccountStorage, index);
    }
}
