// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;
pragma abicoder v2;

import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";
import "@openzeppelin/contracts-upgradeable/utils/math/SafeMathUpgradeable.sol";
import "./interfaces/IBusinessZoneAccountStorage.sol";
import "./interfaces/IContractManager.sol";
import "./interfaces/Struct.sol";
import "./interfaces/Error.sol";
import "./interfaces/Constant.sol";
import "./remigration/RemigrationLib.sol";

/**
 * @dev BusinessZoneAccountStorageコントラクト
 *      BusinessZoneAccountデータのストレージ管理を行う
 *      CRUDのみを実装し、ビジネスロジックは含まない
 */
contract BusinessZoneAccountStorage is Initializable, IBusinessZoneAccountStorage {
    using RemigrationLib for *;
    using SafeMathUpgradeable for uint256;

    ///////////////////////////////////
    // private variables
    ///////////////////////////////////

    /** @dev ContractManagerアドレス */
    IContractManager private _contractManager;

    /** @dev BusinessZoneAccountLogicコントラクトアドレス */
    address private _businessZoneAccountLogicAddr;

    /** @dev アカウントデータ(zoneId => accountId => BusinessZoneAccountData) */
    mapping(uint16 => mapping(bytes32 => BusinessZoneAccountData)) private _businessZoneAccountData;

    /** @dev ビジネスゾーンごとのアカウントID存在確認フラグ */
    mapping(uint16 => mapping(bytes32 => bool)) private _accountIdExistenceByZoneId;

    /** @dev GET ALL関数の最大取得件数 */
    uint256 private constant _GET_BIZACCOUNTS_LIMIT = 1000;

    /* @dev setBizAccountsAllのsignature検証用 */
    string private constant _SET_BIZACCOUNTS_ALL_SIGNATURE = "setBizAccountsAll";

    ///////////////////////////////////
    // modifiers
    ///////////////////////////////////

    /**
     * @dev BusinessZoneAccountLogicコントラクトからのみ呼び出し可能
     */
    modifier onlyBusinessZoneAccountLogic() {
        require(msg.sender == _businessZoneAccountLogicAddr, Error.GA0012_NOT_ACCOUNT_CONTRACT);
        _;
    }

    /**
     * @dev Admin権限チェック
     */
    modifier adminOnly(
        bytes32 hash,
        uint256 deadline,
        bytes memory signature
    ) {
        (bool has, string memory err) = _contractManager.accessCtrl().checkAdminRole(
            hash,
            deadline,
            signature
        );
        require(bytes(err).length == 0, err);
        require(has, Error.RV0001_ACTRL_BAD_ROLE);
        _;
    }

    ///////////////////////////////////
    // functions
    ///////////////////////////////////

    /**
     * @dev 初期化関数
     *
     * @param contractManager ContractManagerアドレス
     * @param businessZoneAccountLogicAddr BusinessZoneAccountLogicコントラクトアドレス
     */
    function initialize(IContractManager contractManager, address businessZoneAccountLogicAddr)
        public
        initializer
    {
        require(
            address(contractManager) != address(0) && businessZoneAccountLogicAddr != address(0),
            Error.RV0006_ISSUER_INVALID_VAL
        );
        _contractManager = contractManager;
        _businessZoneAccountLogicAddr = businessZoneAccountLogicAddr;
    }

    /**
     * @dev コントラクトバージョン取得
     *
     * @return コントラクトバージョン
     */
    function version() external pure returns (string memory) {
        return "v1";
    }

    ///////////////////////////////////
    // BusinessZoneAccountData CRUD操作
    ///////////////////////////////////

    /**
     * @dev BusinessZoneAccountデータを取得する
     * @param zoneId ゾーンID
     * @param accountId アカウントID
     * @return businessZoneAccountData BusinessZoneAccountデータ
     */
    function getBusinessZoneAccountData(uint16 zoneId, bytes32 accountId)
        external
        view
        override
        returns (BusinessZoneAccountData memory businessZoneAccountData)
    {
        return _businessZoneAccountData[zoneId][accountId];
    }

    /**
     * @dev BusinessZoneAccountデータを設定する
     * @param zoneId ゾーンID
     * @param accountId アカウントID
     * @param businessZoneAccountData BusinessZoneAccountデータ
     */
    function setBusinessZoneAccountData(
        uint16 zoneId,
        bytes32 accountId,
        BusinessZoneAccountData memory businessZoneAccountData
    ) external override onlyBusinessZoneAccountLogic {
        _businessZoneAccountData[zoneId][accountId] = businessZoneAccountData;
    }

    /**
     * @dev アカウントステータスを更新する
     * @param zoneId ゾーンID
     * @param accountId アカウントID
     * @param accountStatus アカウントステータス
     */
    function updateAccountStatus(
        uint16 zoneId,
        bytes32 accountId,
        bytes32 accountStatus
    ) external override onlyBusinessZoneAccountLogic {
        _businessZoneAccountData[zoneId][accountId].accountStatus = accountStatus;
    }

    /**
     * @dev アカウント名を更新する
     * @param zoneId ゾーンID
     * @param accountId アカウントID
     * @param accountName アカウント名
     */
    function updateAccountName(
        uint16 zoneId,
        bytes32 accountId,
        string memory accountName
    ) external override onlyBusinessZoneAccountLogic {
        _businessZoneAccountData[zoneId][accountId].accountName = accountName;
    }

    /**
     * @dev 申込日時を更新する
     * @param zoneId ゾーンID
     * @param accountId アカウントID
     * @param appliedAt 申込日時
     */
    function updateAppliedAt(
        uint16 zoneId,
        bytes32 accountId,
        uint256 appliedAt
    ) external override onlyBusinessZoneAccountLogic {
        _businessZoneAccountData[zoneId][accountId].appliedAt = appliedAt;
    }

    /**
     * @dev 登録日時を更新する
     * @param zoneId ゾーンID
     * @param accountId アカウントID
     * @param registeredAt 登録日時
     */
    function updateRegisteredAt(
        uint16 zoneId,
        bytes32 accountId,
        uint256 registeredAt
    ) external override onlyBusinessZoneAccountLogic {
        _businessZoneAccountData[zoneId][accountId].registeredAt = registeredAt;
    }

    /**
     * @dev 解約申込日時を更新する
     * @param zoneId ゾーンID
     * @param accountId アカウントID
     * @param terminatingAt 解約申込日時
     */
    function updateTerminatingAt(
        uint16 zoneId,
        bytes32 accountId,
        uint256 terminatingAt
    ) external override onlyBusinessZoneAccountLogic {
        _businessZoneAccountData[zoneId][accountId].terminatingAt = terminatingAt;
    }

    /**
     * @dev 解約日時を更新する
     * @param zoneId ゾーンID
     * @param accountId アカウントID
     * @param terminatedAt 解約日時
     */
    function updateTerminatedAt(
        uint16 zoneId,
        bytes32 accountId,
        uint256 terminatedAt
    ) external override onlyBusinessZoneAccountLogic {
        _businessZoneAccountData[zoneId][accountId].terminatedAt = terminatedAt;
    }

    /**
     * @dev 残高を更新する
     * @param zoneId ゾーンID
     * @param accountId アカウントID
     * @param balance 新しい残高
     */
    function updateBalance(
        uint16 zoneId,
        bytes32 accountId,
        uint256 balance
    ) external override onlyBusinessZoneAccountLogic {
        _businessZoneAccountData[zoneId][accountId].balance = balance;
    }

    /**
     * @dev 残高を増加させる
     * @param zoneId ゾーンID
     * @param accountId アカウントID
     * @param amount 増加金額
     */
    function increaseBalance(
        uint16 zoneId,
        bytes32 accountId,
        uint256 amount
    ) external override onlyBusinessZoneAccountLogic {
        _businessZoneAccountData[zoneId][accountId].balance += amount;
    }

    /**
     * @dev 残高を減少させる
     * @param zoneId ゾーンID
     * @param accountId アカウントID
     * @param amount 減少金額
     */
    function decreaseBalance(
        uint16 zoneId,
        bytes32 accountId,
        uint256 amount
    ) external override onlyBusinessZoneAccountLogic {
        _businessZoneAccountData[zoneId][accountId].balance -= amount;
    }

    ///////////////////////////////////
    // アカウント存在確認管理
    ///////////////////////////////////

    /**
     * @dev アカウント存在確認フラグを取得する
     * @param zoneId ゾーンID
     * @param accountId アカウントID
     * @return exists 存在確認フラグ
     */
    function getAccountIdExistenceByZoneId(uint16 zoneId, bytes32 accountId)
        external
        view
        override
        returns (bool exists)
    {
        return _accountIdExistenceByZoneId[zoneId][accountId];
    }

    /**
     * @dev アカウント存在確認フラグを設定する
     * @param zoneId ゾーンID
     * @param accountId アカウントID
     * @param exists 存在確認フラグ
     */
    function setAccountIdExistenceByZoneId(
        uint16 zoneId,
        bytes32 accountId,
        bool exists
    ) external override onlyBusinessZoneAccountLogic {
        _accountIdExistenceByZoneId[zoneId][accountId] = exists;
    }

    ///////////////////////////////////
    // バックアップ・リストア用
    ///////////////////////////////////

    /**
     * @dev 全BusinessZoneAccountデータを設定する（バックアップ・リストア用）
     * @param bizAccounts 全BusinessZoneAccountデータ
     * @param deadline 署名期限
     * @param signature 署名
     */
    function setBizAccountsAll(
        BizAccountsAll memory bizAccounts,
        uint256 deadline,
        bytes memory signature
    )
        external
        override
        adminOnly(
            keccak256(abi.encode(_SET_BIZACCOUNTS_ALL_SIGNATURE, deadline)),
            deadline,
            signature
        )
    {
        RemigrationLib.setBizAccountsAll(
            _businessZoneAccountData,
            _accountIdExistenceByZoneId,
            bizAccounts
        );
    }

    /**
     * @dev 全BusinessZoneAccountデータを取得する（バックアップ・リストア用）
     * @param index インデックス
     * @return bizAccounts 全BusinessZoneAccountデータ
     */
    function getBizAccountsAll(uint256 index)
        external
        view
        override
        returns (BizAccountsAll memory bizAccounts)
    {
        return
            RemigrationLib.getBizAccountsAll(
                _businessZoneAccountData,
                _accountIdExistenceByZoneId,
                address(_contractManager),
                index
            );
    }
}
