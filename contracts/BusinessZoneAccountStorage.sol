// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;
pragma abicoder v2;

import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";
import "@openzeppelin/contracts-upgradeable/utils/math/SafeMathUpgradeable.sol";
import "./interfaces/IBusinessZoneAccountStorage.sol";
import "./interfaces/IContractManager.sol";
import "./interfaces/Struct.sol";
import "./interfaces/Error.sol";
import "./remigration/RemigrationLib.sol";

/**
 * @dev BusinessZoneAccountStorageコントラクト
 *      BusinessZoneAccountデータのストレージ管理を行う
 *      CRUDのみを実装し、ビジネスロジックは含まない
 */
contract BusinessZoneAccountStorage is Initializable, IBusinessZoneAccountStorage {
    using RemigrationLib for *;
    using SafeMathUpgradeable for uint256;

    ///////////////////////////////////
    // private variables
    ///////////////////////////////////

    /** @dev ContractManagerアドレス */
    IContractManager private _contractManager;

    /** @dev BusinessZoneAccountLogicコントラクトアドレス */
    address private _businessZoneAccountLogicAddr;

    /** @dev アカウントデータ(zoneId => accountId => BusinessZoneAccountData) */
    mapping(uint16 => mapping(bytes32 => BusinessZoneAccountData)) private _businessZoneAccountData;

    /** @dev ビジネスゾーンごとのアカウントID存在確認フラグ */
    mapping(uint16 => mapping(bytes32 => bool)) private _accountIdExistenceByZoneId;

    /** @dev GET ALL関数の最大取得件数 */
    uint256 private constant _GET_BIZACCOUNTS_LIMIT = 1000;

    /* @dev setBizAccountsAllのsignature検証用 */
    string private constant _SET_BIZACCOUNTS_ALL_SIGNATURE = "setBizAccountsAll";

    ///////////////////////////////////
    // modifiers
    ///////////////////////////////////

    /**
     * @dev BusinessZoneAccountLogicコントラクトからのみ呼び出し可能
     */
    modifier onlyBusinessZoneAccountLogic() {
        require(
            msg.sender == _businessZoneAccountLogicAddr,
            Error.GA0012_NOT_ACCOUNT_CONTRACT
        );
        _;
    }

    /**
     * @dev Admin権限チェック
     */
    modifier adminOnly(
        bytes32 hash,
        uint256 deadline,
        bytes memory signature
    ) {
        (bool has, string memory err) = _contractManager.accessCtrl().checkAdminRole(
            hash,
            deadline,
            signature
        );
        require(bytes(err).length == 0, err);
        require(has, Error.RV0001_ACTRL_BAD_ROLE);
        _;
    }

    ///////////////////////////////////
    // functions
    ///////////////////////////////////

    /**
     * @dev 初期化関数
     *
     * @param contractManager ContractManagerアドレス
     * @param businessZoneAccountLogicAddr BusinessZoneAccountLogicコントラクトアドレス
     */
    function initialize(
        IContractManager contractManager,
        address businessZoneAccountLogicAddr
    ) public initializer {
        require(
            address(contractManager) != address(0) && businessZoneAccountLogicAddr != address(0),
            Error.RV0006_ISSUER_INVALID_VAL
        );
        _contractManager = contractManager;
        _businessZoneAccountLogicAddr = businessZoneAccountLogicAddr;
    }

    /**
     * @dev コントラクトバージョン取得
     *
     * @return コントラクトバージョン
     */
    function version() external pure returns (string memory) {
        return "v1";
    }

    ///////////////////////////////////
    // BusinessZoneAccountData CRUD操作
    ///////////////////////////////////

    /**
     * @dev BusinessZoneAccountデータを取得する
     * @param zoneId ゾーンID
     * @param accountId アカウントID
     * @return businessZoneAccountData BusinessZoneAccountデータ
     */
    function getBusinessZoneAccountData(uint16 zoneId, bytes32 accountId)
        external
        view
        override
        returns (BusinessZoneAccountData memory businessZoneAccountData)
    {
        return _businessZoneAccountData[zoneId][accountId];
    }

    /**
     * @dev BusinessZoneAccountデータを設定する
     * @param zoneId ゾーンID
     * @param accountId アカウントID
     * @param businessZoneAccountData BusinessZoneAccountデータ
     */
    function setBusinessZoneAccountData(
        uint16 zoneId,
        bytes32 accountId,
        BusinessZoneAccountData memory businessZoneAccountData
    ) external override onlyBusinessZoneAccountLogic {
        _businessZoneAccountData[zoneId][accountId] = businessZoneAccountData;
    }

    /**
     * @dev アカウントをアクティブ状態に設定する
     * @param zoneId ゾーンID
     * @param accountId アカウントID
     */
    function setActivateAccount(uint16 zoneId, bytes32 accountId)
        external
        override
        onlyBusinessZoneAccountLogic
    {
        _businessZoneAccountData[zoneId][accountId].accountStatus = Constant._STATUS_ACTIVE;
        _businessZoneAccountData[zoneId][accountId].registeredAt = block.timestamp;
    }

    /**
     * @dev アカウントを解約状態に設定する
     * @param zoneId ゾーンID
     * @param accountId アカウントID
     */
    function setBizZoneTerminated(uint16 zoneId, bytes32 accountId)
        external
        override
        onlyBusinessZoneAccountLogic
    {
        _businessZoneAccountData[zoneId][accountId].accountStatus = Constant._STATUS_TERMINATED;
        _businessZoneAccountData[zoneId][accountId].terminatedAt = block.timestamp;
    }

    /**
     * @dev アカウントステータスを設定する
     * @param zoneId ゾーンID
     * @param accountId アカウントID
     * @param accountStatus アカウントステータス
     */
    function setBusinessAccountStatus(
        uint16 zoneId,
        bytes32 accountId,
        bytes32 accountStatus
    ) external override onlyBusinessZoneAccountLogic {
        _businessZoneAccountData[zoneId][accountId].accountStatus = accountStatus;
    }

    /**
     * @dev ビジネスゾーンアカウント情報を同期する
     * @param zoneId ゾーンID
     * @param accountId アカウントID
     * @param accountName アカウント名
     * @param accountStatus アカウントステータス
     */
    function syncBusinessZoneStatus(
        uint16 zoneId,
        bytes32 accountId,
        string memory accountName,
        bytes32 accountStatus
    ) external override onlyBusinessZoneAccountLogic {
        if (accountStatus == Constant._STATUS_APPLIYNG) {
            _businessZoneAccountData[zoneId][accountId].accountName = accountName;
            _businessZoneAccountData[zoneId][accountId].appliedAt = block.timestamp;
        } else if (accountStatus == Constant._STATUS_TERMINATING) {
            _businessZoneAccountData[zoneId][accountId].terminatingAt = block.timestamp;
        }

        _accountIdExistenceByZoneId[zoneId][accountId] = true;
        _businessZoneAccountData[zoneId][accountId].accountStatus = accountStatus;
    }

    /**
     * @dev ビジネスゾーン残高を同期する
     * @param zoneId ゾーンID
     * @param toAccountId 送り先アカウントID
     * @param fromAccountId 送り元アカウントID
     * @param amount 金額
     */
    function syncBusinessZoneBalance(
        uint16 zoneId,
        bytes32 toAccountId,
        bytes32 fromAccountId,
        uint256 amount
    ) external override onlyBusinessZoneAccountLogic {
        _businessZoneAccountData[zoneId][toAccountId].balance += amount;
        _businessZoneAccountData[zoneId][fromAccountId].balance -= amount;
    }

    /**
     * @dev ビジネスゾーン残高を追加する
     * @param zoneId ゾーンID
     * @param accountId アカウントID
     * @param amount 追加金額
     */
    function addBusinessZoneBalance(
        uint16 zoneId,
        bytes32 accountId,
        uint256 amount
    ) external override onlyBusinessZoneAccountLogic {
        _businessZoneAccountData[zoneId][accountId].balance += amount;
    }

    /**
     * @dev ビジネスゾーン残高を減算する
     * @param zoneId ゾーンID
     * @param accountId アカウントID
     * @param amount 減算金額
     */
    function subtractBusinessZoneBalance(
        uint16 zoneId,
        bytes32 accountId,
        uint256 amount
    ) external override onlyBusinessZoneAccountLogic {
        _businessZoneAccountData[zoneId][accountId].balance -= amount;
    }

    /**
     * @dev 残高を更新(償却)
     * @param zoneId ゾーンID
     * @param accountId アカウントID
     * @param amount 償却金額
     */
    function balanceUpdateByRedeemVoucher(
        uint16 zoneId,
        bytes32 accountId,
        uint256 amount
    ) external override onlyBusinessZoneAccountLogic {
        require(
            _businessZoneAccountData[zoneId][accountId].balance >= amount,
            Error.UE4402_BALANCE_NOT_ENOUGH
        );
        _businessZoneAccountData[zoneId][accountId].balance -= amount;
    }

    /**
     * @dev 残高を更新(発行)
     * @param zoneId ゾーンID
     * @param accountId アカウントID
     * @param amount 発行金額
     */
    function balanceUpdateByIssueVoucher(
        uint16 zoneId,
        bytes32 accountId,
        uint256 amount
    ) external override onlyBusinessZoneAccountLogic {
        _businessZoneAccountData[zoneId][accountId].balance += amount;
    }

    /**
     * @dev 強制償却による残高更新
     * @param zoneId ゾーンID
     * @param accountId アカウントID
     * @return burnedAmount 償却された金額
     */
    function balanceUpdateByForceBurn(uint16 zoneId, bytes32 accountId)
        external
        override
        onlyBusinessZoneAccountLogic
        returns (uint256 burnedAmount)
    {
        burnedAmount = _businessZoneAccountData[zoneId][accountId].balance;
        _businessZoneAccountData[zoneId][accountId].balance = 0;
        _businessZoneAccountData[zoneId][accountId].accountStatus = Constant._STATUS_FORCE_BURNED;
        return burnedAmount;
    }
